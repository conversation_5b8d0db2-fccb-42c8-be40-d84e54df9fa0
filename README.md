# Distributed Kafka Consumer with Ray, Daft, and Iceberg ELT Pipeline

A comprehensive data processing platform that combines distributed Kafka consumption with modern data lake technologies to implement a medallion architecture (Bronze-Silver-Gold) for ELT processing.

## 🏗️ Architecture Overview

This project extends the original distributed Kafka consumer with:
- **Ray**: Distributed computing for parallel processing
- **Daft**: Fast columnar data processing with Python-native API
- **Iceberg**: ACID-compliant data lake with schema evolution
- **MinIO**: S3-compatible object storage
- **Nessie**: Data catalog with version control and branch management
- **Airflow**: Workflow orchestration for ELT pipelines

### Medallion Architecture (Bronze-Silver-Gold)

1. **Bronze Layer**: Raw data ingestion from Kafka with minimal transformation
2. **Silver Layer**: Cleaned, validated, and standardized data
3. **Gold Layer**: Business-ready analytics and aggregated metrics

## 🚀 Key Features

- **Real-time Data Ingestion**: Kafka to Iceberg with batching and partitioning
- **Distributed Processing**: Ray actors for parallel data processing
- **Data Quality**: Built-in validation and quality checks
- **Schema Evolution**: Automatic schema management with Iceberg
- **Time Travel**: Query historical data with Iceberg snapshots
- **Orchestration**: Airflow DAGs for automated ELT workflows
- **Monitoring**: Comprehensive logging and metrics

## 📋 System Requirements

- **Python**: 3.11+
- **Ray**: 2.48.0+
- **Docker**: 20.10+ (for infrastructure)
- **Docker Compose**: 2.0+
- **Memory**: 8GB+ recommended
- **CPU**: 4+ cores recommended

## 🛠️ Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd ray-distributed-kafka-consumer-python

# Copy environment configuration
cp .env.example .env

# Install dependencies
pip install -r requirements-dev.txt
pip install -e .
```

### 2. Start Infrastructure Services

```bash
# Start all services (Kafka, MinIO, Nessie, Airflow, PostgreSQL, Redis)
docker-compose up -d

# Wait for services to be ready (check health)
docker-compose ps
```

### 3. Initialize Ray Cluster

```bash
# Start Ray head node
ray start --head --port=6379 --dashboard-host=0.0.0.0

# For distributed setup, start worker nodes:
# ray start --address='ray://localhost:10001'
```

### 4. Configure and Start Kafka Consumer

```bash
# Set environment variables
export WORKER_CONFIG_PATH=config/daft_iceberg_consumer_config.json
export LOCAL_MODE=Y

# Start the consumer application
uvicorn src.event_consumer_app:app --port 8000 --reload
```

## 📁 Configuration Files

### Kafka Consumer Configuration

The project includes two consumer configurations:

1. **Original Configuration**: `config/consumer_config.json` - Basic console output
2. **Daft-Iceberg Configuration**: `config/daft_iceberg_consumer_config.json` - Full ELT pipeline

### Example Daft-Iceberg Configuration

```json
{
  "consumer_name": "user_events_consumer",
  "topic_name": "user-events",
  "number_of_workers": 3,
  "sink_configs": {
    "transformer_cls": "src.transformers.daft_iceberg_transformer.DaftIcebergTransformer",
    "stream_writers": ["src.stream_writers.daft_iceberg_writer.BatchedDaftIcebergWriter"],
    "batch_size": 1000,
    "batch_timeout_seconds": 30,
    "namespace": "bronze",
    "auto_create_tables": true
  }
}
```

## 🔄 ELT Pipeline Workflow

### Real-time Bronze Layer (Kafka → Iceberg)

1. **Kafka Consumer**: Ray actors consume messages from Kafka topics
2. **Daft Transformation**: Messages are processed using Daft DataFrames
3. **Metadata Enrichment**: Kafka metadata and processing timestamps added
4. **Batching**: Records are batched for efficient writes (1000 records or 30s timeout)
5. **Iceberg Write**: Batches written to partitioned Iceberg tables in MinIO

### Batch Silver Layer (Bronze → Silver)

1. **Airflow Scheduler**: Triggers daily processing at 2 AM
2. **Data Quality**: Validation, deduplication, and cleaning
3. **Schema Standardization**: Consistent data types and formats
4. **Ray Processing**: Distributed processing across multiple workers
5. **Silver Tables**: Clean, validated data ready for analytics

### Batch Gold Layer (Silver → Gold)

1. **Airflow Scheduler**: Triggers daily processing at 4 AM
2. **Business Logic**: Aggregations and KPI calculations
3. **Analytics Creation**: User behavior, transaction metrics, system health
4. **Ray Processing**: Distributed analytics processing
5. **Gold Tables**: Business-ready datasets for dashboards and reports

## 🛠️ API Endpoints

The FastAPI application provides management endpoints:

- `POST /manager/start-consumers` - Start all consumer workers
- `POST /manager/stop-consumers` - Stop all consumer workers
- `GET /manager/fetch-consumers` - Get running consumer status
- `POST /manager/health` - Health check endpoint

## 📊 Monitoring and Observability

### Ray Dashboard
- Access at `http://localhost:8265`
- Monitor worker health, resource usage, and task execution

### Airflow UI
- Access at `http://localhost:8080` (admin/admin)
- Monitor DAG execution, task status, and logs

### MinIO Console
- Access at `http://localhost:9001` (minioadmin/minioadmin)
- Browse data lake contents and manage buckets

### Nessie UI
- Access at `http://localhost:19120`
- View catalog contents, branches, and commit history

## 🧪 Testing

### Run Unit Tests

```bash
# Run all tests
./scripts/run_tests.sh

# Run specific test file
pytest tests/test_daft_iceberg_transformer.py -v

# Run with coverage
pytest tests/ --cov=src --cov-report=html
```

### Integration Testing

```bash
# Start infrastructure
docker-compose up -d

# Wait for services to be ready
sleep 30

# Create test topics
docker exec kafka kafka-topics --create --topic user-events --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1

# Send test messages
echo '{"user_id": 123, "action": "login", "timestamp": "2024-01-15T10:30:00Z"}' | \
  docker exec -i kafka kafka-console-producer --topic user-events --bootstrap-server localhost:9092

# Start consumer and verify data flow
export WORKER_CONFIG_PATH=config/daft_iceberg_consumer_config.json
uvicorn src.event_consumer_app:app --port 8000
```

## 🚀 Production Deployment

### Environment Variables

```bash
# Ray Configuration
export RAY_HEAD_ADDRESS=ray://your-ray-head:10001
export LOCAL_MODE=N
export WORKER_NUM_CPUS=1.0

# Kafka Configuration
export KAFKA_BOOTSTRAP_SERVERS=your-kafka:9092
export KAFKA_SECURITY_PROTOCOL=SASL_SSL
export KAFKA_SASL_USERNAME=your-username
export KAFKA_SASL_PASSWORD=your-password

# Storage Configuration
export MINIO_ENDPOINT=your-s3-endpoint
export MINIO_ACCESS_KEY=your-access-key
export MINIO_SECRET_KEY=your-secret-key

# Catalog Configuration
export NESSIE_URI=https://your-nessie:19120/api/v1
export ICEBERG_WAREHOUSE_PATH=s3a://your-bucket/warehouse
```

### Scaling Considerations

1. **Ray Cluster**: Scale workers based on throughput requirements
2. **Kafka Partitions**: Increase partitions for higher parallelism
3. **Batch Sizes**: Tune batch sizes based on latency vs. throughput needs
4. **Resource Allocation**: Monitor CPU/memory usage and adjust accordingly

## 🔧 Troubleshooting

### Common Issues

1. **Ray Connection Issues**
   ```bash
   # Check Ray cluster status
   ray status

   # Restart Ray cluster
   ray stop
   ray start --head --port=6379
   ```

2. **Kafka Connection Issues**
   ```bash
   # Test Kafka connectivity
   docker exec kafka kafka-topics --list --bootstrap-server localhost:9092
   ```

3. **MinIO/Nessie Issues**
   ```bash
   # Check service health
   docker-compose ps
   docker-compose logs minio
   docker-compose logs nessie
   ```

4. **Airflow DAG Issues**
   ```bash
   # Check DAG status
   docker exec airflow-scheduler airflow dags list

   # View logs
   docker-compose logs airflow-scheduler
   ```

## 📚 Additional Resources

- [Ray Documentation](https://docs.ray.io/)
- [Daft Documentation](https://www.getdaft.io/projects/docs/)
- [Apache Iceberg Documentation](https://iceberg.apache.org/)
- [Project Nessie Documentation](https://projectnessie.org/)
- [Apache Airflow Documentation](https://airflow.apache.org/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE.txt](LICENSE.txt) file for details.
```json
[
  {
    "consumer_name": "some_consumer_group_name",
    "topic_name": "first-topic",
    "number_of_workers": 2,
    "enable_auto_commit": false,
    "bootstrap_servers": "localhost:9092",
    "key_deserializer": "STRING_DES",
    "value_deserializer": "STRING_DES",
    "header_deserializer": null,
    "auto_offset_reset": "earliest",
    "max_poll_records": 20,
    "max_poll_interval_ms": 60000,
    "sink_configs": {
      "transformer_cls": "src.transformers.test_transformer.SampleTransformer",
      "num_retries": 3,
      "retry_delay_seconds": 1,
      "stream_writers": [
        "src.stream_writers.console_stream_writer.ConsoleStreamWriter"
      ]
    },
    "dlq_config": {
      "bootstrap_servers": "localhost:9092",
      "topic_name": "test-dlq",
      "key_serializer": "STRING_SER",
      "value_serializer": "STRING_SER",
      "acks": "all",
      "compression_type": "gzip",
      "retries": 3,
      "linger_ms": 10
    }
  }
]

```

Config info

Config Name|Description|default value|Is mandatory?|
-----------|-----------|------------|--------------|
consumer_name|This will be used as consumer group name| |Yes
number_of_workers|Number of consumers to create for a consumer group|1|No
sink_configs|Any config related to your sink task. Say, if your are writing to Elasticsearch then you may want to add ES endpoint in config| |Yes
dlq_config|Dead letter queue config| |No
For available Serializers/deserializers refer [ser_des_util.py](https://github.com/bkatwal/distributed-kafka-consumer-python/blob/main/src/kafka_core/ser_des_util.py)

Rest of the configs are self explanatory. 

**<ins>Step 3 - Install the Requirements</ins>**

Install all dependencies in [requirement.txt](https://github.com/bkatwal/distributed-kafka-consumer-python/blob/main/requirements.txt)
```shell
pip install -r <path/to/requirement.txt>
```

Install the code using `setup.py`.
This is needed for ray to find modules to pickle/unpickle.

Go to project root folder, where setup.py exists and run:
```shell
 pip install -e .
```

**<ins>Step 4 - Start ray head node</ins>**

If running in local, run below command:
```shell
 ray start --head --port=6379
```


**<ins>Step 5 - Set necessary Environment Variables</ins>**

Variable Name|Description|Is Mandatory?|Default Value|
-------------|------------|------------|-------------|
LOCAL_MODE| `Y` or `N`. Tells weather to run Kafka Consumer in single node or in a distributed setup.|N|Y|
RAY_HEAD_ADDRESS|Ex: `ray://************:10001`. Avoid creating this env variable, if head and driver/app running on same node|No|auto|
WORKER_CONFIG_PATH|worker [json conig](https://github.com/bkatwal/distributed-kafka-consumer-python/blob/main/config/consumer_config.json) path|Yes||
APP_USERNAME|Username to setup Basic API Authentication|No|admin|
APP_PASSWORD|Password to setup Basic API Authentication|No|admin|
WORKER_NUM_CPUS|Number of CPUs to reserve per Consumer/Worker|No|0.25|
SECURITY_PROTOCOL|Pass the security protocol being used to connect to Kafka Brokers. Valid values are - PLAINTEXT, SASL_PLAINTEXT, SASL_SSL|No|None|
SASL_MECHANISM|Using SASL based Auth. Pass either of the valid values - PLAIN, SCRAM-SHA-256, SCRAM-SHA-512|No|None|
SASL_USERNAME|Pass SASL username if using SASL Auth to connect to Kafka|No|None|
SASL_PASSWORD|Pass SASL password if using SASL Auth to connect to Kafka|No|None

**<ins>Step 6 - Run the APP</ins>**
```shell
uvicorn src.event_consumer_app:app --port <port> --reload
```

**Run App in docker container**

<ins>Build Image</ins>
```shell
# run below in the project root folder
 build -t kafka-connect-ray .
```

<ins>Run Image</ins>
```shell
# add other environment variables as you need.
 docker run -e RAY_HEAD_ADDRESS=ray://localhost:10001 -e LOCAL_MODE=N  -dp 8002:8002 kafka-connect-ray
```

**IMPORTANT!!!!**

While creating ray cluster make sure to install code dependencies by running below command in 
your Node or VM or container:
```shell
pip install kafka-connect-dependency==0.1.1
```
This will let ray head and worker nodes find the modules. 

This setup is added in Ray K8 [cluster config yaml](https://github.com/bkatwal/distributed-kafka-consumer-python/blob/main/k8/ray/ray-cluster-config.yaml#L74) file.

### License

The MIT License (MIT)

Copyright (c) Bikas Katwal - <EMAIL>

Permission is hereby granted, free of charge, to any person obtaining a copy of this software and
associated documentation files (the "Software"), to deal in the Software without restriction,
including without limitation the rights to use, copy, modify, merge, publish, distribute,
sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all copies or substantial
portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT
NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES
OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.


# How data flows:

## Kafka to Iceberg Data Flow with Ray and Daft

```mermaid
graph TB
    %% Data Sources
    subgraph "Data Sources"
        K1[Kafka Topic 1\nuser_events]
        K2[Kafka Topic 2\ntransaction_logs]
        K3[Kafka Topic N\nsystem_metrics]
    end
    
    %% Ray Cluster Processing
    subgraph "Ray Cluster - Real-time Processing"
        direction TB
        CW1[Ray Actor\nConsumerWorker-1]
        CW2[Ray Actor\nConsumerWorker-2]
        CW3[Ray Actor\nConsumerWorker-N]
        
        subgraph "New Components"
            DIT[DaftIcebergTransformer\n• JSON parsing\n• Daft DataFrame creation\n• Metadata enrichment]
            DISW[BatchedDaftIcebergWriter\n• Batch accumulation (1000 records)\n• Daft transformations\n• Iceberg writes]
        end
    end
    
    %% Infrastructure Layer
    subgraph "Infrastructure Services"
        direction LR
        MINIO[MinIO\nS3-compatible storage\nData lake backend]
        NESSIE[Nessie\nData catalog\nVersion control\nBranch management]
        KAFKA[Kafka Cluster\nMessage streaming]
    end
    
    %% Iceberg Data Lake - Bronze Layer
    subgraph "Bronze Layer (Raw Data)"
        direction TB
        IB1[Iceberg Table\nbronze.user_events\nPartitioned: year/month/day]
        IB2[Iceberg Table\nbronze.transaction_logs\nPartitioned: year/month/day]
        IB3[Iceberg Table\nbronze.system_metrics\nPartitioned: year/month/day]
    end
    
    %% Airflow Orchestration
    subgraph "Airflow Orchestration"
        direction TB
        DAG1[Bronze→Silver DAG\n• Data validation\n• Schema standardization\n• Quality checks]
        DAG2[Silver→Gold DAG\n• Business logic\n• Aggregations\n• Analytics prep]
        
        subgraph "Ray Tasks"
            RT1[Ray Task\nSilver Processing\nDaft transformations]
            RT2[Ray Task\nGold Processing\nDaft aggregations]
        end
    end
    
    %% Silver Layer
    subgraph "Silver Layer (Cleaned Data)"
        direction TB
        IS1[Iceberg Table\nsilver.user_events_clean\n• Validated data\n• Standardized schema]
        IS2[Iceberg Table\nsilver.transaction_logs_clean\n• Deduplication\n• Data quality rules]
        IS3[Iceberg Table\nsilver.system_metrics_clean\n• Normalized metrics\n• Outlier handling]
    end
    
    %% Gold Layer
    subgraph "Gold Layer (Analytics Ready)"
        direction TB
        IG1[Iceberg Table\ngold.user_behavior_summary\n• Daily aggregations\n• Business KPIs]
        IG2[Iceberg Table\ngold.financial_metrics\n• Transaction summaries\n• Revenue analytics]
        IG3[Iceberg Table\ngold.system_health_dashboard\n• Performance metrics\n• Alert thresholds]
    end
    
    %% Data Flow - Real-time (Bronze)
    K1 --> CW1
    K2 --> CW2
    K3 --> CW3
    
    CW1 --> DIT
    CW2 --> DIT
    CW3 --> DIT
    
    DIT --> DISW
    DISW --> NESSIE
    NESSIE --> IB1
    NESSIE --> IB2
    NESSIE --> IB3
    
    IB1 --> MINIO
    IB2 --> MINIO
    IB3 --> MINIO
    
    %% Data Flow - Batch Processing (Silver/Gold)
    DAG1 --> RT1
    RT1 --> IS1
    RT1 --> IS2
    RT1 --> IS3
    
    DAG2 --> RT2
    RT2 --> IG1
    RT2 --> IG2
    RT2 --> IG3
    
    IB1 -.-> DAG1
    IB2 -.-> DAG1
    IB3 -.-> DAG1
    
    IS1 -.-> DAG2
    IS2 -.-> DAG2
    IS3 -.-> DAG2
    
    IS1 --> MINIO
    IS2 --> MINIO
    IS3 --> MINIO
    IG1 --> MINIO
    IG2 --> MINIO
    IG3 --> MINIO
    
    %% Styling
    classDef kafka fill:#ff6b6b,stroke:#333,stroke-width:2px,color:#fff
    classDef ray fill:#4ecdc4,stroke:#333,stroke-width:2px,color:#fff
    classDef infra fill:#a55eea,stroke:#333,stroke-width:2px,color:#fff
    classDef bronze fill:#cd7f32,stroke:#333,stroke-width:2px,color:#fff
    classDef silver fill:#c0c0c0,stroke:#333,stroke-width:2px,color:#000
    classDef gold fill:#ffd700,stroke:#333,stroke-width:2px,color:#000
    classDef airflow fill:#017cee,stroke:#333,stroke-width:2px,color:#fff
    classDef processing fill:#96ceb4,stroke:#333,stroke-width:2px,color:#000
    
    class K1,K2,K3,KAFKA kafka
    class CW1,CW2,CW3,RT1,RT2 ray
    class MINIO,NESSIE infra
    class IB1,IB2,IB3 bronze
    class IS1,IS2,IS3 silver
    class IG1,IG2,IG3 gold
    class DAG1,DAG2 airflow
    class DIT,DISW processing
```