from setuptools import setup

with open("README.md", "r") as fh:
    long_description = fh.read()

setup(
    name='kafka-connect-dependency',
    version='0.1.1',
    license='MIT',
    url='https://github.com/bkatwal/distributed-kafka-consumer-python',
    author='<PERSON><PERSON><PERSON>',
    author_email='<EMAIL>',
    description='Library to run distributed Kafka Consumers using Ray with Daft and Iceberg',
    long_description='This library need to be installed in ray nodes. So, ray head and worker '
                     'nodes can find and pickle/unpickle Kafka Consumer modules. Extended with '
                     'Daft and Iceberg support for ELT processing with medallion architecture.',
    keywords=['ray', 'kafka', 'consumer', 'daft', 'iceberg', 'elt', 'data-lake'],
    long_description_content_type="text/markdown",
    py_modules=['src.exceptions.usi_exceptions',
                'src.kafka_core.consumer_manager',
                'src.kafka_core.kafka_stream_writer',
                'src.kafka_core.kafka_util',
                'src.kafka_core.ser_des_util',
                'src.kafka_core.sink_task',
                'src.model.worker_dto',
                'src.stream_writers.stream_writer',
                'src.stream_writers.console_stream_writer',
                'src.stream_writers.daft_iceberg_writer',
                'src.transformers.transformer',
                'src.transformers.test_transformer',
                'src.transformers.daft_iceberg_transformer',
                'src.elt.bronze_processor',
                'src.elt.silver_processor',
                'src.elt.gold_processor',
                'src.elt.iceberg_utils',
                'src.utility.common_util',
                'src.utility.config_manager',
                'src.utility.logging_util'],
    python_requires='>=3',
    install_requires=[
        # Core framework dependencies
        'fastapi==0.116.1',
        'uvicorn==0.35.0',
        'cachetools==6.1.0',
        'starlette==0.47.3',
        'pydantic==2.11.7',
        'ratelimit==2.2.1',
        'ray==2.48.0',
        'setuptools==80.9.0',
        'kafka-python==2.2.15',

        # Data processing and analytics
        'daft[all]==0.3.28',
        'pandas==2.2.3',
        'pyarrow==18.1.0',
        'numpy==2.1.3',

        # Iceberg and data lake
        'pyiceberg[all]==0.8.1',
        'boto3==1.35.84',
        's3fs==2024.12.0',

        # MinIO client
        'minio==7.2.11',

        # Nessie client (for Iceberg catalog)
        'pynessie==0.77.0',

        # Additional utilities
        'python-dotenv==1.0.1',
        'sqlalchemy==2.0.36',
        'requests==2.32.3',
        'urllib3==2.2.3'
    ]
)
